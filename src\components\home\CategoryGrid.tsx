import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowR<PERSON>, Sparkles, Heart, Crown, Star, Gift } from 'lucide-react';
import { Link } from 'react-router-dom';

const CategoryGrid = () => {
  const categories = [
    {
      id: 1,
      name: 'Designer <PERSON><PERSON><PERSON>',
      description: 'Elegant & contemporary designs',
      image: '/Designer.webp',
      color: 'from-purple-500 to-pink-500',
      bgGradient: 'bg-gradient-to-br from-purple-50 to-pink-50',
      icon: Crown,
      href: '/category/designer',
      badge: 'Trending',
      badgeColor: 'bg-purple-600'
    },
    {
      id: 2,
      name: 'Kids Rakhi',
      description: 'Fun & colorful for little ones',
      image: '/kids.webp',
      color: 'from-blue-500 to-cyan-500',
      bgGradient: 'bg-gradient-to-br from-blue-50 to-cyan-50',
      icon: Heart,
      href: '/category/kids',
      badge: 'Popular',
      badgeColor: 'bg-blue-600'
    },
    {
      id: 3,
      name: 'Premium Sets',
      description: 'Luxury rakhi with sweets & gifts',
      image: '/premium.webp',
      color: 'from-amber-500 to-orange-500',
      bgGradient: 'bg-gradient-to-br from-amber-50 to-orange-50',
      icon: Star,
      href: '/category/premium',
      badge: 'Exclusive',
      badgeColor: 'bg-amber-600'
    },
    {
      id: 4,
      name: 'Traditional Rakhi',
      description: 'Classic & authentic designs',
      image: '/traditional.webp',
      color: 'from-red-500 to-pink-500',
      bgGradient: 'bg-gradient-to-br from-red-50 to-pink-50',
      icon: Gift,
      href: '/category/traditional',
      badge: 'Heritage',
      badgeColor: 'bg-red-600'
    }
  ];

  return (
    <section className="py-20 lg:py-32 bg-gradient-to-b from-gray-50 via-white to-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center space-x-3 bg-festive-gradient text-white rounded-full px-8 py-3 mb-8 shadow-lg">
            <Sparkles className="h-5 w-5 animate-pulse" />
            <span className="text-base font-bold tracking-wide">EXPLORE COLLECTIONS</span>
            <Sparkles className="h-5 w-5 animate-pulse" />
          </div>
          
          <h2 className="text-5xl lg:text-6xl font-bold text-gray-800 mb-8 font-playfair leading-tight">
            Shop by <span className="bg-festive-gradient bg-clip-text text-transparent">Category</span>
          </h2>
          
          <p className="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Discover our carefully curated collections, each designed to celebrate the unique bond you share with your loved ones
          </p>
          
          {/* Decorative Elements */}
          <div className="flex justify-center items-center mt-8 space-x-4">
            <div className="w-16 h-0.5 bg-festive-gradient rounded-full"></div>
            <div className="w-3 h-3 bg-festive-gradient rounded-full animate-pulse"></div>
            <div className="w-16 h-0.5 bg-festive-gradient rounded-full"></div>
          </div>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-8 lg:gap-10">
          {categories.map((category, index) => (
            <Link key={category.id} to={category.href} className="block h-full">
              <Card
                className={`group hover:shadow-2xl transition-all duration-700 overflow-hidden border-0 bg-white hover:-translate-y-4 transform ${category.bgGradient} hover:bg-white h-full flex flex-col cursor-pointer`}
                style={{
                  animationDelay: `${index * 150}ms`,
                  animation: 'fade-in 0.8s ease-out forwards'
                }}
              >
              <CardContent className="p-0 relative flex flex-col h-full">
                {/* Image Container */}
                <div className="relative h-40 sm:h-60 lg:h-80 overflow-hidden">
                  <div 
                    className="w-full h-full bg-cover bg-center bg-no-repeat group-hover:scale-110 transition-transform duration-700 filter group-hover:brightness-110"
                    style={{
                      backgroundImage: `url('${category.image}')`,
                      backgroundColor: `linear-gradient(135deg, ${category.color.replace('from-', '').replace('to-', '').split(' ')[0]} 0%, ${category.color.replace('from-', '').replace('to-', '').split(' ')[1]} 100%)`
                    }}
                  >
                    
                  </div>
                  
                  {/* Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-60 group-hover:opacity-40 transition-opacity duration-500"></div>
                  
                  {/* Badge */}
                  <div className="absolute top-2 sm:top-4 left-2 sm:left-4">
                    <span className={`${category.badgeColor} text-white text-xs sm:text-sm font-bold px-2 sm:px-4 py-1 sm:py-2 rounded-full shadow-lg backdrop-blur-sm`}>
                      {category.badge}
                    </span>
                  </div>

                  

                  
                </div>

                {/* Content Section */}
                <div className="p-3 sm:p-6 lg:p-10 relative flex-1 flex flex-col">
                  {/* Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="w-full h-full bg-gradient-to-br from-gray-900 to-transparent"></div>
                  </div>
                  
                  <div className="relative z-10 flex flex-col h-full">
                    <h3 className="text-sm sm:text-xl lg:text-3xl font-bold text-gray-800 mb-1 sm:mb-2 lg:mb-4 group-hover:text-festive-red transition-colors duration-500 font-playfair">
                      {category.name}
                    </h3>

                    <p className="text-gray-600 mb-3 sm:mb-4 lg:mb-8 text-xs sm:text-sm lg:text-lg leading-relaxed flex-1">
                      {category.description}
                    </p>

                    {/* CTA Button */}
                    <div className="mt-auto">
                      <div
                        className="w-full group-hover:bg-festive-gradient group-hover:text-white group-hover:shadow-xl transition-all duration-500 bg-transparent border-2 border-gray-300 text-gray-700 hover:border-transparent font-bold py-2 sm:py-3 lg:py-4 px-2 sm:px-4 lg:px-6 rounded-lg sm:rounded-xl lg:rounded-2xl text-xs sm:text-sm lg:text-lg text-center"
                      >
                        <span className="flex items-center justify-center gap-2 w-full">
                          <span className="hidden sm:inline">Explore Collection</span>
                          <span className="sm:hidden">Explore</span>
                          <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4 lg:h-5 lg:w-5 group-hover:translate-x-1 transition-transform duration-500" />
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Decorative Elements */}
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-gray-300 to-transparent group-hover:via-festive-red transition-colors duration-500"></div>
              </CardContent>
            </Card>
            </Link>
          ))}
        </div>

        {/* View All Categories Button */}
        <div className="text-center mt-8 lg:mt-12">
          <Link to="/products">
            <Button
              size="lg"
              className="bg-festive-gradient hover:opacity-90 text-white px-6 sm:px-12 py-3 sm:py-6 rounded-2xl font-bold text-base sm:text-xl transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl"
            >
              <Gift className="mr-2 sm:mr-3 h-5 w-5 sm:h-6 sm:w-6" />
              View All Categories
              <ArrowRight className="ml-2 sm:ml-3 h-5 w-5 sm:h-6 sm:w-6" />
            </Button>
          </Link>
        </div>

        
      </div>
    </section>
  );
};

export default CategoryGrid;